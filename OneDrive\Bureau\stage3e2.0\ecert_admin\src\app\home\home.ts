import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-home',
  imports: [CommonModule],
  templateUrl: './home.html',
  styleUrl: './home.css'
})
export class Home {
  // Propriétés du composant
  username: string = '<PERSON>'; // Changez ce nom selon vos besoins
  userRole: string = 'Administrateur';
  notificationCount: number = 3;

  constructor(private router: Router) {}

  // Méthodes pour les interactions
  openNotifications(): void {
    console.log('Ouverture des notifications');
    // Ici vous pouvez ajouter la logique pour ouvrir un panneau de notifications
    // ou naviguer vers une page de notifications
  }

  openShop(): void {
    console.log('Ouverture du shop');
    // Ici vous pouvez ajouter la logique pour ouvrir le shop
    // ou naviguer vers une page de shop
  }

  navigateTo(section: string): void {
    console.log(`Navigation vers: ${section}`);

    // Navigation vers les différentes sections
    switch (section) {
      case 'commander':
        // this.router.navigate(['/commander']);
        console.log('Redirection vers la page Commander');
        break;
      case 'mes-commandes':
        // this.router.navigate(['/mes-commandes']);
        console.log('Redirection vers la page Mes Commandes');
        break;
      case 'messages':
        // this.router.navigate(['/messages']);
        console.log('Redirection vers la page Messages');
        break;
      case 'organisation':
        // this.router.navigate(['/organisation']);
        console.log('Redirection vers la page Organisation');
        break;
      case 'contrat':
        // this.router.navigate(['/contrat']);
        console.log('Redirection vers la page Contrat');
        break;
      case 'gestion-utilisateur':
        // this.router.navigate(['/gestion-utilisateur']);
        console.log('Redirection vers la page Gestion Utilisateur');
        break;
      default:
        console.log('Section non reconnue');
    }
  }

  // Méthode pour mettre à jour le nom d'utilisateur
  updateUsername(newUsername: string): void {
    this.username = newUsername;
  }

  // Méthode pour mettre à jour le nombre de notifications
  updateNotificationCount(count: number): void {
    this.notificationCount = count;
  }

  // Méthode pour réinitialiser les notifications
  clearNotifications(): void {
    this.notificationCount = 0;
  }
}
