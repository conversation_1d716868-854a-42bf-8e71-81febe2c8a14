/* Admin Dashboard Styles */
.admin-dashboard {
  min-height: 100vh;
 background: rgba(255, 255, 255, 0.95);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Header Styles */
.dashboard-header {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.header-left {
  display: flex;
  align-items: center;
}
.logo-image {
  height: 120px;
  width: auto;
  max-width: 300px;
  border-radius: 8px;
  background: white;
  padding: 0.5rem;
  object-fit: contain;
  display: block;
  position: relative;
  z-index: 10;
  border: 1px solid rgba(255, 255, 255, 0.2);
}
.logo-section {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.app-name {
  font-size: 1.5rem;
  font-weight: 1000;
  color: #2d3748;
  letter-spacing: -0.025em;
}
.company-tagline {
  font-size: 0.9rem;
  opacity: 0.9;
  margin: 0.25rem 0 0 0;
  font-weight: 400;
}
.header-right {
  display: flex;
  align-items: center;
  gap: 1.5rem;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.user-avatar {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-details {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.username {
  font-size: 0.95rem;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.user-role {
  font-size: 0.8rem;
  font-weight: 400;
  color: #718096;
  margin: 0;
}

.header-icons {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.icon-container {
  position: relative;
  padding: 0.75rem;
  border-radius: 12px;
  background: rgba(102, 126, 234, 0.1);
  border: 1px solid rgba(102, 126, 234, 0.2);
  cursor: pointer;
  transition: all 0.3s ease;
  color: #667eea;
}

.icon-container:hover {
  background: rgba(102, 126, 234, 0.2);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.notification-badge {
  position: absolute;
  top: -5px;
  right: -5px;
  background: #e53e3e;
  color: white;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 10px;
  min-width: 20px;
  text-align: center;
}

/* Main Content Styles */
.dashboard-main {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-top: 1rem;
}

/* Dashboard Box Styles */
.dashboard-box {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  padding: 2rem;
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
}

.dashboard-box::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.dashboard-box:hover::before {
  transform: scaleX(1);
}

.dashboard-box:hover {
  transform: translateY(-8px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  background: rgba(255, 255, 255, 1);
}

.box-icon {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: white;
  transition: all 0.3s ease;
}

.dashboard-box:hover .box-icon {
  transform: scale(1.1);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.box-title {
  font-size: 1.25rem;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 0.5rem 0;
  letter-spacing: -0.025em;
}

.box-description {
  font-size: 0.95rem;
  color: #718096;
  margin: 0;
  line-height: 1.5;
}

/* Responsive Design */
@media (max-width: 768px) {
  .dashboard-header {
    padding: 1rem;
    flex-direction: column;
    gap: 1rem;
  }

  .header-right {
    width: 100%;
    justify-content: space-between;
  }

  .dashboard-main {
    padding: 1rem;
  }

  .dashboard-grid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .dashboard-box {
    padding: 1.5rem;
  }

  .app-name {
    font-size: 1.25rem;
  }
}

@media (max-width: 480px) {
  .dashboard-header {
    padding: 0.75rem;
  }

  .logo {
    width: 32px;
    height: 32px;
  }

  .app-name {
    font-size: 1.1rem;
  }

  .username {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
  }

  .icon-container {
    padding: 0.6rem;
  }

  .dashboard-box {
    padding: 1.25rem;
  }

  .box-icon {
    width: 50px;
    height: 50px;
  }

  .box-title {
    font-size: 1.1rem;
  }

  .box-description {
    font-size: 0.9rem;
  }
}