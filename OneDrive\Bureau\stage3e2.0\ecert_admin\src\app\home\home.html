<div class="admin-dashboard">
  <!-- Header Section -->
  <header class="dashboard-header">
    <div class="header-left">
      <div class="logo-section">
         <img src="/tuntrust-logo.png" alt="TunTrust Logo" class="logo-image">
        <span class="app-name">TunTrust </span>
        <div class="logo-text">
        
        <p class="company-tagline">Plateforme de Certification Électronique</p>
      </div>
      </div>
    </div>

    <div class="header-right">
      <div class="user-info">
        
        <div class="user-details">
          <span class="username">{{ username }}</span>
          <span class="user-role">{{ userRole }}</span>
        </div>
      </div>

      <div class="header-icons">
        <div class="icon-container notification-icon" (click)="openNotifications()">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22ZM18 16V11C18 7.93 16.36 5.36 13.5 4.68V4C13.5 3.17 12.83 2.5 12 2.5C11.17 2.5 10.5 3.17 10.5 4V4.68C7.63 5.36 6 7.92 6 11V16L4 18V19H20V18L18 16Z" fill="currentColor"/>
          </svg>
          <span class="notification-badge" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
        </div>

        <div class="icon-container shop-icon" (click)="openShop()">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 18C5.9 18 5.01 18.9 5.01 20C5.01 21.1 5.9 22 7 22C8.1 22 9 21.1 9 20C9 18.9 8.1 18 7 18ZM1 2V4H3L6.6 11.59L5.24 14.04C5.09 14.32 5 14.65 5 15C5 16.1 5.9 17 7 17H19V15H7.42C7.28 15 7.17 14.89 7.17 14.75L7.2 14.63L8.1 13H15.55C16.3 13 16.96 12.58 17.3 11.97L20.88 5H5.21L4.27 3H1ZM17 18C15.9 18 15.01 18.9 15.01 20C15.01 21.1 15.9 22 17 22C18.1 22 19 21.1 19 20C19 18.9 18.1 18 17 18Z" fill="currentColor"/>
          </svg>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="dashboard-main">
    <div class="dashboard-grid">
      <!-- Commander Box -->
      <div class="dashboard-box" (click)="navigateTo('commander')">
        
        <h3 class="box-title">Commander</h3>
        <p class="box-description">Passer de nouvelles commandes</p>
      </div>

      <!-- Mes Commandes Box -->
      <div class="dashboard-box" (click)="navigateTo('mes-commandes')">
        
        <h3 class="box-title">Mes Commandes</h3>
        <p class="box-description">Consulter l'historique des commandes</p>
      </div>

      <!-- Messages Box -->
      <div class="dashboard-box" (click)="navigateTo('messages')">
        
        <h3 class="box-title">Messages</h3>
        <p class="box-description">Gérer la messagerie</p>
      </div>

      <!-- Organisation Box -->
      <div class="dashboard-box" (click)="navigateTo('organisation')">
        
        <h3 class="box-title">Organisation</h3>
        <p class="box-description">Gérer l'organisation</p>
      </div>

      <!-- Contrat Box -->
      <div class="dashboard-box" (click)="navigateTo('contrat')">
       
        <h3 class="box-title">Contrat</h3>
        <p class="box-description">Gérer les contrats</p>
      </div>

      <!-- Gestion Utilisateur Box -->
      <div class="dashboard-box" (click)="navigateTo('gestion-utilisateur')">
       
        <h3 class="box-title">Gestion Utilisateur</h3>
        <p class="box-description">Administrer les utilisateurs</p>
      </div>
    </div>
  </main>
</div>
