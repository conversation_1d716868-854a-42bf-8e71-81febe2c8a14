<div class="admin-dashboard">
  <!-- Header Section -->
  <header class="dashboard-header">
    <div class="header-left">
      <div class="logo-section">
         <img src="/tuntrust-logo.png" alt="TunTrust Logo" class="logo-image">
        <span class="app-name">eCert Admin</span>
      </div>
    </div>

    <div class="header-right">
      <div class="user-info">
        <span class="username">{{ username }}</span>
      </div>

      <div class="header-icons">
        <div class="icon-container notification-icon" (click)="openNotifications()">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 22C13.1 22 14 21.1 14 20H10C10 21.1 10.9 22 12 22ZM18 16V11C18 7.93 16.36 5.36 13.5 4.68V4C13.5 3.17 12.83 2.5 12 2.5C11.17 2.5 10.5 3.17 10.5 4V4.68C7.63 5.36 6 7.92 6 11V16L4 18V19H20V18L18 16Z" fill="currentColor"/>
          </svg>
          <span class="notification-badge" *ngIf="notificationCount > 0">{{ notificationCount }}</span>
        </div>

        <div class="icon-container shop-icon" (click)="openShop()">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M7 18C5.9 18 5.01 18.9 5.01 20C5.01 21.1 5.9 22 7 22C8.1 22 9 21.1 9 20C9 18.9 8.1 18 7 18ZM1 2V4H3L6.6 11.59L5.24 14.04C5.09 14.32 5 14.65 5 15C5 16.1 5.9 17 7 17H19V15H7.42C7.28 15 7.17 14.89 7.17 14.75L7.2 14.63L8.1 13H15.55C16.3 13 16.96 12.58 17.3 11.97L20.88 5H5.21L4.27 3H1ZM17 18C15.9 18 15.01 18.9 15.01 20C15.01 21.1 15.9 22 17 22C18.1 22 19 21.1 19 20C19 18.9 18.1 18 17 18Z" fill="currentColor"/>
          </svg>
        </div>
      </div>
    </div>
  </header>

  <!-- Main Content -->
  <main class="dashboard-main">
    <div class="dashboard-grid">
      <!-- Commander Box -->
      <div class="dashboard-box" (click)="navigateTo('commander')">
        <div class="box-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 7H16V6C16 4.9 15.1 4 14 4H10C8.9 4 8 4.9 8 6V7H5C4.4 7 4 7.4 4 8S4.4 9 5 9H6V19C6 20.1 6.9 21 8 21H16C17.1 21 18 20.1 18 19V9H19C19.6 9 20 8.6 20 8S19.6 7 19 7ZM10 6H14V7H10V6ZM16 19H8V9H16V19Z" fill="currentColor"/>
          </svg>
        </div>
        <h3 class="box-title">Commander</h3>
        <p class="box-description">Passer de nouvelles commandes</p>
      </div>

      <!-- Mes Commandes Box -->
      <div class="dashboard-box" (click)="navigateTo('mes-commandes')">
        <div class="box-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM17 12H7V10H17V12ZM13 16H7V14H13V16ZM17 8H7V6H17V8Z" fill="currentColor"/>
          </svg>
        </div>
        <h3 class="box-title">Mes Commandes</h3>
        <p class="box-description">Consulter l'historique des commandes</p>
      </div>

      <!-- Messages Box -->
      <div class="dashboard-box" (click)="navigateTo('messages')">
        <div class="box-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M20 2H4C2.9 2 2.01 2.9 2.01 4L2 22L6 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2ZM20 16H5.17L4 17.17V4H20V16ZM7 9H17V11H7V9ZM7 12H15V14H7V12ZM7 6H17V8H7V6Z" fill="currentColor"/>
          </svg>
        </div>
        <h3 class="box-title">Messages</h3>
        <p class="box-description">Gérer la messagerie</p>
      </div>

      <!-- Organisation Box -->
      <div class="dashboard-box" (click)="navigateTo('organisation')">
        <div class="box-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 7V3H2V21H22V7H12ZM6 19H4V17H6V19ZM6 15H4V13H6V15ZM6 11H4V9H6V11ZM6 7H4V5H6V7ZM10 19H8V17H10V19ZM10 15H8V13H10V15ZM10 11H8V9H10V11ZM10 7H8V5H10V7ZM20 19H12V17H20V19ZM20 15H12V13H20V15ZM20 11H12V9H20V11Z" fill="currentColor"/>
          </svg>
        </div>
        <h3 class="box-title">Organisation</h3>
        <p class="box-description">Gérer l'organisation</p>
      </div>

      <!-- Contrat Box -->
      <div class="dashboard-box" (click)="navigateTo('contrat')">
        <div class="box-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M14 2H6C4.9 2 4.01 2.9 4.01 4L4 20C4 21.1 4.89 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20ZM9 13V19H7V13H9ZM15 15V19H17V15H15ZM11 11V19H13V11H11Z" fill="currentColor"/>
          </svg>
        </div>
        <h3 class="box-title">Contrat</h3>
        <p class="box-description">Gérer les contrats</p>
      </div>

      <!-- Gestion Utilisateur Box -->
      <div class="dashboard-box" (click)="navigateTo('gestion-utilisateur')">
        <div class="box-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M16 4C18.2 4 20 5.8 20 8C20 10.2 18.2 12 16 12C13.8 12 12 10.2 12 8C12 5.8 13.8 4 16 4ZM16 14C20.4 14 24 15.8 24 18V20H8V18C8 15.8 11.6 14 16 14ZM8 13C10.2 13 12 11.2 12 9C12 6.8 10.2 5 8 5C5.8 5 4 6.8 4 9C4 11.2 5.8 13 8 13ZM8 15C4.7 15 0 16.6 0 19V21H6V19C6 17.9 6.7 16.3 8 15Z" fill="currentColor"/>
          </svg>
        </div>
        <h3 class="box-title">Gestion Utilisateur</h3>
        <p class="box-description">Administrer les utilisateurs</p>
      </div>
    </div>
  </main>
</div>
